#!/usr/bin/env python3
"""
Quick Coordinate Test
Simple script to test and fix coordinate accuracy issues
"""

import os
import sys
import time
import pyautogui
import mss
from PIL import Image, ImageDraw, ImageFont

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_current_setup():
    """Test current coordinate setup"""
    print("🔍 COORDINATE ACCURACY TEST")
    print("=" * 50)
    
    # Get screen info
    screen_width, screen_height = pyautogui.size()
    print(f"Total screen size: {screen_width} x {screen_height}")
    
    # Get mouse position
    mouse_x, mouse_y = pyautogui.position()
    print(f"Current mouse position: ({mouse_x}, {mouse_y})")
    
    # Get monitor info
    sct = mss.mss()
    monitors = sct.monitors
    print(f"Detected monitors: {len(monitors)-1}")
    
    for i, monitor in enumerate(monitors[1:], 1):
        print(f"  Monitor {i}: {monitor['width']}x{monitor['height']} at ({monitor['left']}, {monitor['top']})")
        
        # Check if mouse is on this monitor
        if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
            monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
            print(f"    ✅ Mouse is currently on this monitor")
            active_monitor = i
            active_monitor_info = monitor
    
    return active_monitor, active_monitor_info, sct

def capture_and_mark_screenshot(monitor_info, sct):
    """Capture screenshot and add coordinate markers"""
    print(f"\n📸 Capturing screenshot from active monitor...")
    
    # Capture screenshot
    screenshot = sct.grab(monitor_info)
    img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
    
    print(f"Screenshot size: {img.size[0]} x {img.size[1]}")
    
    # Create marked version
    marked_img = img.copy()
    draw = ImageDraw.Draw(marked_img)
    
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # Add test coordinates
    test_points = [
        (100, 100, "Top-Left Test"),
        (img.size[0]//2, img.size[1]//2, "Center Test"),
        (img.size[0]-100, img.size[1]-100, "Bottom-Right Test"),
        (200, 300, "Custom Point 1"),
        (600, 400, "Custom Point 2")
    ]
    
    # Mark each test point
    for x, y, label in test_points:
        # Ensure coordinates are within bounds
        x = max(10, min(x, img.size[0]-10))
        y = max(10, min(y, img.size[1]-10))
        
        # Draw crosshair
        size = 20
        draw.line([(x-size, y), (x+size, y)], fill="red", width=3)
        draw.line([(x, y-size), (x, y+size)], fill="red", width=3)
        
        # Draw circle
        draw.ellipse([x-8, y-8, x+8, y+8], outline="yellow", width=2)
        
        # Add label with background
        text_bbox = draw.textbbox((0, 0), f"({x},{y})", font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        label_x, label_y = x + 15, y - 10
        draw.rectangle([label_x-2, label_y-2, label_x+text_width+4, label_y+text_height+4], fill="black")
        draw.text((label_x, label_y), f"({x},{y})", fill="white", font=font)
        draw.text((label_x, label_y+text_height+2), label, fill="cyan", font=font)
    
    # Save both versions
    timestamp = int(time.time())
    original_file = f"test_original_{timestamp}.png"
    marked_file = f"test_marked_{timestamp}.png"
    
    img.save(original_file)
    marked_img.save(marked_file)
    
    print(f"✅ Screenshots saved:")
    print(f"   Original: {original_file}")
    print(f"   Marked: {marked_file}")
    
    return test_points, original_file, marked_file

def test_coordinate_translation(test_points, monitor_info):
    """Test coordinate translation from screenshot to screen"""
    print(f"\n🎯 Testing coordinate translation...")
    print(f"Monitor offset: ({monitor_info['left']}, {monitor_info['top']})")
    
    translation_results = []
    
    for screenshot_x, screenshot_y, label in test_points:
        # Calculate actual screen coordinates
        actual_x = screenshot_x + monitor_info['left']
        actual_y = screenshot_y + monitor_info['top']
        
        print(f"  {label}:")
        print(f"    Screenshot coords: ({screenshot_x}, {screenshot_y})")
        print(f"    Actual coords: ({actual_x}, {actual_y})")
        
        translation_results.append((screenshot_x, screenshot_y, actual_x, actual_y, label))
    
    return translation_results

def interactive_mouse_test(translation_results):
    """Interactive test of mouse movement"""
    print(f"\n🖱️  Interactive mouse movement test")
    print("This will move your mouse to each test coordinate.")
    print("Watch carefully to see if the mouse goes to the right place!")
    
    for screenshot_x, screenshot_y, actual_x, actual_y, label in translation_results:
        print(f"\n📍 Testing: {label}")
        print(f"   Will move mouse to actual coordinates: ({actual_x}, {actual_y})")
        
        # Countdown
        for i in range(3, 0, -1):
            print(f"   Moving in {i}...")
            time.sleep(1)
        
        # Move mouse
        try:
            pyautogui.moveTo(actual_x, actual_y, duration=1.0)
            print(f"   ✅ Mouse moved to ({actual_x}, {actual_y})")
            
            # Verify actual position
            time.sleep(0.5)
            verify_x, verify_y = pyautogui.position()
            
            if abs(verify_x - actual_x) <= 2 and abs(verify_y - actual_y) <= 2:
                print(f"   ✅ Position verified: ({verify_x}, {verify_y})")
            else:
                print(f"   ❌ Position mismatch! Expected ({actual_x}, {actual_y}), got ({verify_x}, {verify_y})")
            
        except Exception as e:
            print(f"   ❌ Error moving mouse: {e}")
        
        # Wait for user confirmation
        input("   Press Enter to continue to next point...")

def diagnose_coordinate_issues():
    """Diagnose common coordinate issues"""
    print(f"\n🔧 COORDINATE ISSUE DIAGNOSIS")
    print("=" * 50)
    
    issues_found = []
    
    # Check screen scaling
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        # Get DPI scaling
        dpi = root.winfo_fpixels('1i')
        scaling = dpi / 96.0  # 96 DPI is 100% scaling
        
        print(f"Screen DPI: {dpi:.1f}")
        print(f"Scaling factor: {scaling:.2f} ({scaling*100:.0f}%)")
        
        if scaling != 1.0:
            issues_found.append(f"Screen scaling is {scaling*100:.0f}% (should be 100% for accurate coordinates)")
        
        root.destroy()
        
    except Exception as e:
        print(f"Could not check screen scaling: {e}")
    
    # Check PyAutoGUI settings
    print(f"\nPyAutoGUI settings:")
    print(f"  Failsafe: {pyautogui.FAILSAFE}")
    print(f"  Pause: {pyautogui.PAUSE}")
    
    # Check for multiple monitors with different scaling
    sct = mss.mss()
    monitors = sct.monitors[1:]  # Skip combined monitor
    
    if len(monitors) > 1:
        print(f"\nMulti-monitor setup detected:")
        for i, monitor in enumerate(monitors, 1):
            print(f"  Monitor {i}: {monitor['width']}x{monitor['height']} at ({monitor['left']}, {monitor['top']})")
        
        # Check for negative coordinates (common issue)
        for monitor in monitors:
            if monitor['left'] < 0 or monitor['top'] < 0:
                issues_found.append(f"Monitor has negative coordinates: ({monitor['left']}, {monitor['top']})")
    
    # Report issues
    if issues_found:
        print(f"\n❌ ISSUES FOUND:")
        for issue in issues_found:
            print(f"  • {issue}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        if any("scaling" in issue for issue in issues_found):
            print("  • Set Windows display scaling to 100% for all monitors")
            print("  • Restart the application after changing scaling")
        
        if any("negative coordinates" in issue for issue in issues_found):
            print("  • Check monitor arrangement in Windows display settings")
            print("  • Ensure primary monitor is positioned correctly")
    else:
        print(f"\n✅ No obvious issues detected!")
    
    return issues_found

def main():
    """Main test function"""
    print("🚀 QUICK COORDINATE ACCURACY TEST")
    print("=" * 60)
    print("This tool will help diagnose coordinate accuracy issues.")
    print()
    
    try:
        # Step 1: Test current setup
        active_monitor, monitor_info, sct = test_current_setup()
        
        # Step 2: Capture and mark screenshot
        test_points, original_file, marked_file = capture_and_mark_screenshot(monitor_info, sct)
        
        # Step 3: Test coordinate translation
        translation_results = test_coordinate_translation(test_points, monitor_info)
        
        # Step 4: Diagnose issues
        issues = diagnose_coordinate_issues()
        
        # Step 5: Interactive test
        print(f"\n" + "="*60)
        choice = input("Do you want to test mouse movement? (y/n): ").lower()
        if choice == 'y':
            interactive_mouse_test(translation_results)
        
        # Step 6: Summary
        print(f"\n📋 TEST SUMMARY")
        print("=" * 30)
        print(f"✅ Screenshot captured: {original_file}")
        print(f"✅ Marked version: {marked_file}")
        print(f"✅ Coordinate translation tested")
        
        if issues:
            print(f"❌ {len(issues)} issues found - see diagnosis above")
        else:
            print(f"✅ No obvious issues detected")
        
        print(f"\n💡 Next steps:")
        print(f"  1. Open {marked_file} to see test coordinates")
        print(f"  2. If mouse movement was inaccurate, check the diagnosis")
        print(f"  3. Run the main solver with: python solver.py")
        
    except KeyboardInterrupt:
        print(f"\n\nTest interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
