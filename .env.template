# Environment Configuration for Coordinate-based Web Browser Agent AI
# Copy this file to .env and fill in your actual values

# Required: Gemini API Key
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Solver Configuration
SOLVER_MAX_ATTEMPTS=3
SOLVER_CONFIDENCE_THRESHOLD=0.7

# Optional: Text-to-Speech Configuration
TTS_ENABLED=true
TTS_RATE=150
TTS_VOLUME=0.8

# Optional: Gemini Model Configuration
GEMINI_MODEL=gemini-2.0-flash-exp
GEMINI_TEMPERATURE=0.1

# Optional: Logging Configuration
LOG_LEVEL=INFO
ENABLE_LOGGING=true

# Optional: Security Settings
SAFE_MODE=true
MAX_DAILY_ATTEMPTS=1000

# Optional: Performance Settings
SCREENSHOT_QUALITY=95
AUTOMATION_PAUSE=0.5
