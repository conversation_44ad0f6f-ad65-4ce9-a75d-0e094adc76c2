#!/usr/bin/env python3
"""
Configuration file for Coordinate-based Web Browser Agent AI
Centralized settings for solver behavior, accessibility, and performance
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class SolverConfig:
    """Main solver configuration"""
    max_attempts: int = 3
    wait_timeout: int = 30
    confidence_threshold: float = 0.7
    retry_delay: int = 2
    screenshot_quality: int = 95
    enable_logging: bool = True
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR

@dataclass
class TTSConfig:
    """Text-to-speech configuration"""
    enabled: bool = True
    rate: int = 150  # Words per minute
    volume: float = 0.8  # 0.0 to 1.0
    voice_id: Optional[str] = None  # System-specific voice ID
    language: str = "en"
    announce_actions: bool = True
    announce_results: bool = True
    announce_instructions: bool = True

@dataclass
class AutomationConfig:
    """Automation behavior configuration"""
    click_duration: float = 0.1
    move_duration: float = 0.5
    pause_between_actions: float = 0.5
    failsafe_enabled: bool = True
    smooth_movement: bool = True
    double_click_interval: float = 0.25

@dataclass
class GeminiConfig:
    """Gemini AI configuration"""
    model_name: str = "gemini-2.0-flash-exp"
    temperature: float = 0.1  # Lower = more deterministic
    max_tokens: int = 2048
    timeout: int = 30
    retry_attempts: int = 2
    safety_settings: Dict = None

@dataclass
class ScreenCaptureConfig:
    """Screen capture configuration"""
    format: str = "PNG"
    quality: int = 95
    compression: int = 6  # PNG compression level
    save_screenshots: bool = True
    screenshot_dir: str = "screenshots"
    max_screenshots: int = 100  # Auto-cleanup after this many

@dataclass
class AccessibilityConfig:
    """Accessibility-specific settings"""
    high_contrast_mode: bool = False
    large_text_mode: bool = False
    screen_reader_compatible: bool = True
    keyboard_navigation: bool = True
    audio_descriptions: bool = True
    verbose_feedback: bool = True

@dataclass
class SecurityConfig:
    """Security and safety settings"""
    allowed_domains: List[str] = None  # None = all domains allowed
    blocked_domains: List[str] = None
    require_user_confirmation: bool = False
    safe_mode: bool = True  # Extra safety checks
    max_daily_attempts: int = 1000
    rate_limiting: bool = True

class ConfigManager:
    """Manages all configuration settings"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or ".solver_config"
        self.load_config()
    
    def load_config(self):
        """Load configuration from file or use defaults"""
        # Default configurations
        self.solver = SolverConfig()
        self.tts = TTSConfig()
        self.automation = AutomationConfig()
        self.gemini = GeminiConfig()
        self.screen_capture = ScreenCaptureConfig()
        self.accessibility = AccessibilityConfig()
        self.security = SecurityConfig()
        
        # Load from environment variables if available
        self._load_from_env()
        
        # Load from config file if exists
        if os.path.exists(self.config_file):
            self._load_from_file()
    
    def _load_from_env(self):
        """Load settings from environment variables"""
        # Solver settings
        if os.getenv('SOLVER_MAX_ATTEMPTS'):
            self.solver.max_attempts = int(os.getenv('SOLVER_MAX_ATTEMPTS'))
        if os.getenv('SOLVER_CONFIDENCE_THRESHOLD'):
            self.solver.confidence_threshold = float(os.getenv('SOLVER_CONFIDENCE_THRESHOLD'))
        
        # TTS settings
        if os.getenv('TTS_ENABLED'):
            self.tts.enabled = os.getenv('TTS_ENABLED').lower() == 'true'
        if os.getenv('TTS_RATE'):
            self.tts.rate = int(os.getenv('TTS_RATE'))
        if os.getenv('TTS_VOLUME'):
            self.tts.volume = float(os.getenv('TTS_VOLUME'))
        
        # Gemini settings
        if os.getenv('GEMINI_MODEL'):
            self.gemini.model_name = os.getenv('GEMINI_MODEL')
        if os.getenv('GEMINI_TEMPERATURE'):
            self.gemini.temperature = float(os.getenv('GEMINI_TEMPERATURE'))
    
    def _load_from_file(self):
        """Load settings from configuration file"""
        try:
            import json
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            # Update configurations from file
            for section, settings in config_data.items():
                if hasattr(self, section):
                    config_obj = getattr(self, section)
                    for key, value in settings.items():
                        if hasattr(config_obj, key):
                            setattr(config_obj, key, value)
                            
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            import json
            config_data = {
                'solver': self.solver.__dict__,
                'tts': self.tts.__dict__,
                'automation': self.automation.__dict__,
                'gemini': self.gemini.__dict__,
                'screen_capture': self.screen_capture.__dict__,
                'accessibility': self.accessibility.__dict__,
                'security': self.security.__dict__
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
            print(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def get_gemini_safety_settings(self):
        """Get Gemini safety settings"""
        if self.gemini.safety_settings:
            return self.gemini.safety_settings
        
        # Default safety settings
        import google.generativeai as genai
        return {
            genai.types.HarmCategory.HARM_CATEGORY_HATE_SPEECH: genai.types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            genai.types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: genai.types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            genai.types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: genai.types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            genai.types.HarmCategory.HARM_CATEGORY_HARASSMENT: genai.types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        }
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return any issues"""
        issues = []
        
        # Validate solver config
        if self.solver.max_attempts < 1:
            issues.append("max_attempts must be at least 1")
        if not 0 < self.solver.confidence_threshold <= 1:
            issues.append("confidence_threshold must be between 0 and 1")
        
        # Validate TTS config
        if not 0 <= self.tts.volume <= 1:
            issues.append("TTS volume must be between 0 and 1")
        if self.tts.rate < 50 or self.tts.rate > 500:
            issues.append("TTS rate should be between 50 and 500 WPM")
        
        # Validate automation config
        if self.automation.click_duration < 0:
            issues.append("click_duration cannot be negative")
        if self.automation.move_duration < 0:
            issues.append("move_duration cannot be negative")
        
        # Validate Gemini config
        if not 0 <= self.gemini.temperature <= 2:
            issues.append("Gemini temperature must be between 0 and 2")
        if self.gemini.max_tokens < 100:
            issues.append("max_tokens should be at least 100")
        
        return issues
    
    def print_config(self):
        """Print current configuration"""
        print("Current Configuration:")
        print("=" * 50)
        
        sections = [
            ("Solver", self.solver),
            ("Text-to-Speech", self.tts),
            ("Automation", self.automation),
            ("Gemini AI", self.gemini),
            ("Screen Capture", self.screen_capture),
            ("Accessibility", self.accessibility),
            ("Security", self.security)
        ]
        
        for section_name, config_obj in sections:
            print(f"\n{section_name}:")
            for key, value in config_obj.__dict__.items():
                print(f"  {key}: {value}")

# Global configuration instance
config = ConfigManager()

# Preset configurations for different use cases
PRESETS = {
    "accessibility_focused": {
        "tts": {
            "enabled": True,
            "rate": 120,
            "volume": 1.0,
            "announce_actions": True,
            "announce_results": True,
            "announce_instructions": True,
            "verbose_feedback": True
        },
        "solver": {
            "max_attempts": 5,
            "confidence_threshold": 0.8
        },
        "accessibility": {
            "high_contrast_mode": True,
            "large_text_mode": True,
            "audio_descriptions": True,
            "verbose_feedback": True
        }
    },
    
    "performance_focused": {
        "solver": {
            "max_attempts": 2,
            "confidence_threshold": 0.6,
            "retry_delay": 1
        },
        "automation": {
            "click_duration": 0.05,
            "move_duration": 0.2,
            "pause_between_actions": 0.2
        },
        "gemini": {
            "temperature": 0.0,
            "timeout": 15
        }
    },
    
    "security_focused": {
        "security": {
            "require_user_confirmation": True,
            "safe_mode": True,
            "max_daily_attempts": 100,
            "rate_limiting": True
        },
        "solver": {
            "confidence_threshold": 0.9,
            "max_attempts": 2
        }
    }
}

def apply_preset(preset_name: str):
    """Apply a preset configuration"""
    if preset_name not in PRESETS:
        raise ValueError(f"Unknown preset: {preset_name}")
    
    preset = PRESETS[preset_name]
    
    for section, settings in preset.items():
        if hasattr(config, section):
            config_obj = getattr(config, section)
            for key, value in settings.items():
                if hasattr(config_obj, key):
                    setattr(config_obj, key, value)
    
    print(f"Applied preset: {preset_name}")

def main():
    """Configuration management CLI"""
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python config.py [show|save|preset <name>|validate]")
        return
    
    command = sys.argv[1]
    
    if command == "show":
        config.print_config()
    elif command == "save":
        config.save_config()
    elif command == "validate":
        issues = config.validate_config()
        if issues:
            print("Configuration issues found:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("Configuration is valid")
    elif command == "preset" and len(sys.argv) > 2:
        preset_name = sys.argv[2]
        try:
            apply_preset(preset_name)
            config.save_config()
        except ValueError as e:
            print(f"Error: {e}")
            print(f"Available presets: {', '.join(PRESETS.keys())}")
    else:
        print("Invalid command")

if __name__ == "__main__":
    main()
