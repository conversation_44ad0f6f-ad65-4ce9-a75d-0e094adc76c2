# Core Dependencies
requests==2.31.0           # HTTP requests for Gemini API
pyautogui==0.9.54         # GUI automation for clicking
pyttsx3==2.90            # Text-to-speech engine
mss==9.0.1               # Screen capture
python-dotenv==1.0.0     # Environment variables

# AI/ML Dependencies
google-generativeai==0.4.1  # Official Gemini API client

# Supporting Packages
Pillow==10.2.0           # Image processing (for pyautogui)
pynput==1.7.6            # Keyboard listener (optional for hotkeys)
numpy>=1.26.4            # Required for some image processing
opencv-python==4.9.0.80  # Optional for advanced image analysis

# Development Dependencies (optional)
pytest==8.1.1            # Testing framework
black==24.3.0            # Code formatting
flake8==7.0.0            # Code linting
mypy==1.9.0              # Type checking

google-generativeai>=0.4.1
pyautogui
