#!/usr/bin/env python3
"""
Coordinate Debugging Tool
Comprehensive debugging for coordinate accuracy issues
"""

import os
import sys
import time
import json
import pyautogui
import mss
from PIL import Image, ImageDraw, ImageFont
import tkinter as tk
from tkinter import ttk, messagebox

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class CoordinateDebugger:
    """Debug coordinate accuracy issues"""
    
    def __init__(self):
        self.sct = mss.mss()
        self.monitors = self.sct.monitors
        self.current_screenshot = None
        self.debug_log = []
    
    def log_debug(self, message):
        """Log debug message"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.debug_log.append(log_entry)
        print(log_entry)
    
    def capture_with_overlay(self, monitor_id=None):
        """Capture screenshot with coordinate overlay"""
        try:
            if monitor_id is None:
                # Detect active monitor
                mouse_x, mouse_y = pyautogui.position()
                for i, monitor in enumerate(self.monitors[1:], 1):
                    if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
                        monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
                        monitor_id = i
                        break
                else:
                    monitor_id = 1
            
            monitor = self.monitors[monitor_id]
            self.log_debug(f"Capturing from monitor {monitor_id}: {monitor}")
            
            # Capture screenshot
            screenshot = self.sct.grab(monitor)
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            # Add coordinate grid overlay
            img_with_overlay = self.add_coordinate_overlay(img)
            
            # Save both versions
            timestamp = int(time.time())
            original_path = f"debug_original_{timestamp}.png"
            overlay_path = f"debug_overlay_{timestamp}.png"
            
            img.save(original_path)
            img_with_overlay.save(overlay_path)
            
            self.log_debug(f"Screenshots saved: {original_path}, {overlay_path}")
            self.current_screenshot = img
            
            return img, monitor
            
        except Exception as e:
            self.log_debug(f"Capture error: {e}")
            return None, None
    
    def add_coordinate_overlay(self, img):
        """Add coordinate grid and markers to image"""
        overlay_img = img.copy()
        draw = ImageDraw.Draw(overlay_img)
        
        width, height = img.size
        
        try:
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        # Draw grid lines every 100 pixels
        grid_spacing = 100
        
        # Vertical lines
        for x in range(0, width, grid_spacing):
            draw.line([(x, 0), (x, height)], fill="red", width=1)
            if x > 0:
                draw.text((x+2, 2), str(x), fill="red", font=font)
        
        # Horizontal lines
        for y in range(0, height, grid_spacing):
            draw.line([(0, y), (width, y)], fill="red", width=1)
            if y > 0:
                draw.text((2, y+2), str(y), fill="red", font=font)
        
        # Draw corner markers
        corner_size = 20
        corners = [
            (0, 0, "TL"),
            (width-corner_size, 0, "TR"),
            (0, height-corner_size, "BL"),
            (width-corner_size, height-corner_size, "BR")
        ]
        
        for x, y, label in corners:
            draw.rectangle([x, y, x+corner_size, y+corner_size], outline="blue", width=2)
            draw.text((x+2, y+2), label, fill="blue", font=font)
        
        # Draw center marker
        center_x, center_y = width // 2, height // 2
        marker_size = 10
        draw.rectangle([center_x-marker_size, center_y-marker_size, 
                       center_x+marker_size, center_y+marker_size], 
                      outline="green", width=2)
        draw.text((center_x+12, center_y-6), f"CENTER\n({center_x},{center_y})", 
                 fill="green", font=font)
        
        return overlay_img
    
    def test_coordinate_accuracy(self, test_coordinates):
        """Test accuracy of given coordinates"""
        if not self.current_screenshot:
            self.log_debug("No screenshot available. Capture one first.")
            return
        
        img = self.current_screenshot.copy()
        draw = ImageDraw.Draw(img)
        
        try:
            font = ImageFont.truetype("arial.ttf", 14)
        except:
            font = ImageFont.load_default()
        
        # Mark test coordinates on image
        for i, (x, y, description) in enumerate(test_coordinates):
            # Draw crosshair
            size = 15
            draw.line([(x-size, y), (x+size, y)], fill="yellow", width=3)
            draw.line([(x, y-size), (x, y+size)], fill="yellow", width=3)
            
            # Draw circle
            draw.ellipse([x-5, y-5, x+5, y+5], outline="red", width=2)
            
            # Add label
            label = f"{i+1}: ({x},{y})\n{description}"
            draw.text((x+10, y-10), label, fill="white", font=font)
            
            self.log_debug(f"Marked coordinate {i+1}: ({x}, {y}) - {description}")
        
        # Save marked image
        timestamp = int(time.time())
        marked_path = f"debug_marked_{timestamp}.png"
        img.save(marked_path)
        self.log_debug(f"Marked coordinates saved: {marked_path}")
        
        return marked_path
    
    def interactive_coordinate_picker(self):
        """Interactive GUI for picking and testing coordinates"""
        root = tk.Tk()
        root.title("Coordinate Debugger")
        root.geometry("600x500")
        
        # Variables
        self.picked_coordinates = []
        
        # Create GUI elements
        ttk.Label(root, text="Coordinate Debugging Tool", font=("Arial", 16)).pack(pady=10)
        
        # Monitor selection
        monitor_frame = ttk.Frame(root)
        monitor_frame.pack(pady=5)
        ttk.Label(monitor_frame, text="Monitor:").pack(side=tk.LEFT)
        monitor_var = tk.StringVar(value="Auto-detect")
        monitor_combo = ttk.Combobox(monitor_frame, textvariable=monitor_var)
        monitor_combo['values'] = ["Auto-detect"] + [f"Monitor {i}" for i in range(1, len(self.monitors))]
        monitor_combo.pack(side=tk.LEFT, padx=5)
        
        # Capture button
        def capture_screenshot():
            monitor_id = None if monitor_var.get() == "Auto-detect" else int(monitor_var.get().split()[1])
            img, monitor = self.capture_with_overlay(monitor_id)
            if img:
                status_var.set(f"Captured {img.size[0]}x{img.size[1]} from monitor {monitor_id or 'auto'}")
        
        ttk.Button(root, text="Capture Screenshot", command=capture_screenshot).pack(pady=5)
        
        # Status
        status_var = tk.StringVar(value="Ready")
        ttk.Label(root, textvariable=status_var).pack(pady=5)
        
        # Coordinate input
        coord_frame = ttk.Frame(root)
        coord_frame.pack(pady=10)
        
        ttk.Label(coord_frame, text="Test Coordinates:").pack()
        
        # X, Y input
        input_frame = ttk.Frame(coord_frame)
        input_frame.pack(pady=5)
        
        ttk.Label(input_frame, text="X:").pack(side=tk.LEFT)
        x_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=x_var, width=8).pack(side=tk.LEFT, padx=2)
        
        ttk.Label(input_frame, text="Y:").pack(side=tk.LEFT)
        y_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=y_var, width=8).pack(side=tk.LEFT, padx=2)
        
        ttk.Label(input_frame, text="Description:").pack(side=tk.LEFT)
        desc_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=desc_var, width=15).pack(side=tk.LEFT, padx=2)
        
        def add_coordinate():
            try:
                x = int(x_var.get())
                y = int(y_var.get())
                desc = desc_var.get() or f"Point {len(self.picked_coordinates)+1}"
                self.picked_coordinates.append((x, y, desc))
                coord_list.insert(tk.END, f"({x}, {y}) - {desc}")
                x_var.set("")
                y_var.set("")
                desc_var.set("")
            except ValueError:
                messagebox.showerror("Error", "Invalid coordinates")
        
        ttk.Button(input_frame, text="Add", command=add_coordinate).pack(side=tk.LEFT, padx=5)
        
        # Coordinate list
        list_frame = ttk.Frame(coord_frame)
        list_frame.pack(pady=5, fill=tk.BOTH, expand=True)
        
        coord_list = tk.Listbox(list_frame, height=8)
        coord_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=coord_list.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        coord_list.config(yscrollcommand=scrollbar.set)
        
        # Action buttons
        button_frame = ttk.Frame(root)
        button_frame.pack(pady=10)
        
        def test_coordinates():
            if not self.picked_coordinates:
                messagebox.showwarning("Warning", "No coordinates to test")
                return
            
            marked_path = self.test_coordinate_accuracy(self.picked_coordinates)
            if marked_path:
                status_var.set(f"Coordinates marked in {marked_path}")
        
        def test_mouse_movement():
            if not self.picked_coordinates:
                messagebox.showwarning("Warning", "No coordinates to test")
                return
            
            self.test_mouse_movement_sequence(self.picked_coordinates)
        
        def clear_coordinates():
            self.picked_coordinates.clear()
            coord_list.delete(0, tk.END)
        
        ttk.Button(button_frame, text="Mark on Image", command=test_coordinates).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Test Mouse Movement", command=test_mouse_movement).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear", command=clear_coordinates).pack(side=tk.LEFT, padx=5)
        
        # Debug log
        log_frame = ttk.Frame(root)
        log_frame.pack(pady=5, fill=tk.BOTH, expand=True)
        
        ttk.Label(log_frame, text="Debug Log:").pack(anchor=tk.W)
        log_text = tk.Text(log_frame, height=6)
        log_text.pack(fill=tk.BOTH, expand=True)
        
        def update_log():
            log_text.delete(1.0, tk.END)
            log_text.insert(tk.END, "\n".join(self.debug_log[-20:]))  # Show last 20 entries
            root.after(1000, update_log)  # Update every second
        
        update_log()
        
        root.mainloop()
    
    def test_mouse_movement_sequence(self, coordinates):
        """Test mouse movement to coordinates with visual feedback"""
        self.log_debug("Starting mouse movement test...")
        
        for i, (x, y, description) in enumerate(coordinates):
            self.log_debug(f"Moving to coordinate {i+1}: ({x}, {y}) - {description}")
            
            # Move mouse
            pyautogui.moveTo(x, y, duration=1.0)
            
            # Wait and get feedback
            time.sleep(2)
            
            # Check actual mouse position
            actual_x, actual_y = pyautogui.position()
            self.log_debug(f"Actual mouse position: ({actual_x}, {actual_y})")
            
            if abs(actual_x - x) > 5 or abs(actual_y - y) > 5:
                self.log_debug(f"WARNING: Mouse position mismatch! Expected ({x}, {y}), got ({actual_x}, {actual_y})")
    
    def save_debug_report(self):
        """Save comprehensive debug report"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "monitors": self.monitors,
            "debug_log": self.debug_log,
            "screen_info": {
                "total_size": pyautogui.size(),
                "mouse_position": pyautogui.position()
            }
        }
        
        with open("coordinate_debug_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        self.log_debug("Debug report saved to coordinate_debug_report.json")

def main():
    """Main debugging interface"""
    debugger = CoordinateDebugger()
    
    print("Coordinate Debugging Tool")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Capture screenshot with overlay")
        print("2. Interactive coordinate picker")
        print("3. Test specific coordinates")
        print("4. Save debug report")
        print("5. Exit")
        
        choice = input("Enter choice (1-5): ").strip()
        
        try:
            if choice == "1":
                img, monitor = debugger.capture_with_overlay()
                if img:
                    print(f"Screenshot captured: {img.size[0]}x{img.size[1]}")
            
            elif choice == "2":
                debugger.interactive_coordinate_picker()
            
            elif choice == "3":
                x = int(input("Enter X coordinate: "))
                y = int(input("Enter Y coordinate: "))
                desc = input("Enter description: ") or "Test point"
                
                debugger.test_coordinate_accuracy([(x, y, desc)])
                debugger.test_mouse_movement_sequence([(x, y, desc)])
            
            elif choice == "4":
                debugger.save_debug_report()
            
            elif choice == "5":
                break
            
            else:
                print("Invalid choice")
        
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
