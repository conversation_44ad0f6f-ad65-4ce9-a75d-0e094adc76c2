# Coordinate-based Web Browser Agent AI - Setup Guide

## Overview
This system provides an intelligent web browser automation solution that helps visually impaired employees solve Cloudflare and reCAPTCHA challenges using Google's Gemini 2.5 Flash AI.

## Features
- 🤖 AI-powered challenge detection and solving
- 🎯 Precise coordinate-based clicking
- 🔊 Text-to-speech accessibility support
- 📸 Automatic screenshot analysis
- 🔄 Continuous monitoring mode
- 📝 Comprehensive logging

## Step-by-Step Setup Procedure

### 1. Environment Setup

#### Install Python Dependencies
```bash
pip install -r requirement.txt
```

#### Get Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

#### Create Environment File
Create a `.env` file in the project directory:
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

### 2. Directory Structure
```
reCaptchaSolver/
├── solver.py              # Main solver script
├── captcha_solver.py      # Existing solver (if any)
├── requirement.txt        # Dependencies
├── .env                   # Environment variables
├── screenshots/           # Auto-created for screenshots
├── solver.log            # Auto-created for logs
└── SETUP_GUIDE.md        # This file
```

### 3. Usage Instructions

#### Basic Usage
```python
from solver import CaptchaSolver

# Initialize solver
solver = CaptchaSolver(api_key="your_gemini_api_key")

# Solve current challenge on screen
success = solver.solve_challenge()
```

#### Command Line Usage
```bash
python solver.py
```

Then choose from:
1. Solve current challenge
2. Continuous monitoring
3. Solve Cloudflare specifically
4. Solve reCAPTCHA specifically

### 4. How It Works - Step by Step

#### Challenge Detection Process:
1. **Screen Capture**: Takes screenshot of current browser window
2. **AI Analysis**: Sends image to Gemini 2.5 Flash for analysis
3. **Coordinate Extraction**: AI identifies clickable elements and provides coordinates
4. **Execution**: Automatically clicks at specified coordinates
5. **Verification**: Checks if challenge was solved successfully
6. **Feedback**: Provides audio feedback for accessibility

#### Supported Challenge Types:
- ✅ Cloudflare "Checking your browser" challenges
- ✅ reCAPTCHA v2 (image selection)
- ✅ reCAPTCHA v3 (invisible)
- ✅ hCaptcha challenges
- ✅ Custom bot detection systems

### 5. Accessibility Features

#### Text-to-Speech Support:
- Announces challenge detection
- Provides step-by-step instructions
- Reports success/failure status
- Describes actions being performed

#### Visual Impairment Support:
- No need to see the screen
- Audio-guided interaction
- Automatic challenge detection
- Voice feedback for all operations

### 6. Configuration Options

#### Solver Settings:
```python
solver = CaptchaSolver(api_key)
solver.max_attempts = 3              # Maximum retry attempts
solver.wait_timeout = 30             # Timeout for waiting
solver.confidence_threshold = 0.7    # Minimum confidence for clicks
```

#### TTS Settings:
```python
solver.tts.engine.setProperty('rate', 150)    # Speech rate
solver.tts.engine.setProperty('volume', 0.8)  # Volume level
```

### 7. Advanced Usage

#### Continuous Monitoring:
```python
# Monitor for challenges every 5 seconds
solver.continuous_monitoring(interval=5)
```

#### Custom Region Capture:
```python
# Capture specific screen region
region = {'top': 100, 'left': 100, 'width': 800, 'height': 600}
screenshot = solver.screen_capture.capture_screen(region)
```

#### Manual Coordinate Clicking:
```python
from solver import ClickCoordinate

coord = ClickCoordinate(
    x=500, y=300, 
    confidence=0.9, 
    description="Submit button",
    element_type="button"
)
solver.automation.click_coordinate(coord)
```

### 8. Multi-Monitor Setup

#### Dual/Multi-Monitor Configuration:

The solver automatically detects and handles multi-monitor setups. If you're experiencing issues with coordinates being applied to the wrong screen:

**Quick Setup:**
```bash
# Run the monitor setup utility
python monitor_setup.py

# Choose option 6: "Interactive setup wizard"
# Follow the prompts to configure your monitors
```

**Manual Configuration:**
1. **List Monitors**: Run `python monitor_setup.py` and choose option 1
2. **Detect Active Monitor**: The system detects which monitor has your mouse cursor
3. **Test Coordinates**: Use option 5 to test coordinate translation
4. **Switch Monitors**: Use the main solver's option 5 to switch target monitors

**Common Multi-Monitor Issues:**

**Screenshot from wrong monitor:**
- The solver captures from the monitor where your mouse cursor is located
- Move your mouse to the target monitor before running the solver
- Or use `solver.switch_monitor(monitor_number)` to manually select

**Clicks on wrong monitor:**
- Coordinates are automatically translated based on monitor offset
- Use the monitor setup utility to test coordinate translation
- Check that monitor detection is working correctly

**Monitor Detection Problems:**
```python
# Debug monitor setup
from solver import CaptchaSolver
solver = CaptchaSolver(api_key)
solver.list_monitors()           # List all monitors
solver.debug_mouse_position()    # Check current mouse monitor
solver.switch_monitor(2)         # Switch to monitor 2
```

### 9. Troubleshooting

#### Common Issues:

**API Key Error:**
```
Error: GEMINI_API_KEY not found in environment variables
```
Solution: Ensure `.env` file exists with correct API key

**Screen Capture Error:**
```
Screen capture error: [Errno 13] Permission denied
```
Solution: Run with administrator privileges on Windows

**Click Accuracy Issues:**
- Ensure screen scaling is 100% on all monitors
- Check monitor resolution settings
- Verify PyAutoGUI failsafe is not triggered
- Use monitor setup utility to test coordinate translation

**Multi-Monitor Coordinate Issues:**
```
# Test coordinate translation
python monitor_setup.py
# Choose option 4 or 5 to test coordinates
```

**TTS Not Working:**
```
TTS error: No module named 'pyttsx3'
```
Solution: Install TTS dependencies: `pip install pyttsx3`

### 9. Safety Features

#### Built-in Safeguards:
- PyAutoGUI failsafe (move mouse to corner to stop)
- Confidence threshold for clicks
- Maximum attempt limits
- Comprehensive error logging
- Safe coordinate validation

#### Emergency Stop:
- Move mouse to top-left corner (PyAutoGUI failsafe)
- Press Ctrl+C in terminal
- Close the application window

### 10. Logging and Monitoring

#### Log Files:
- `solver.log`: Detailed operation logs
- `screenshots/`: Saved challenge screenshots
- Console output: Real-time status updates

#### Log Levels:
- INFO: Normal operations
- WARNING: Low confidence actions
- ERROR: Failed operations
- DEBUG: Detailed debugging info

### 11. Performance Optimization

#### Tips for Better Performance:
1. Use dedicated monitor for challenges
2. Ensure stable internet connection
3. Close unnecessary applications
4. Use SSD for faster screenshot processing
5. Optimize screen resolution (1920x1080 recommended)

### 12. Legal and Ethical Considerations

#### Important Notes:
- Use only on websites you own or have permission to test
- Respect website terms of service
- This tool is for accessibility assistance
- Not for bypassing legitimate security measures
- Intended for visually impaired users' assistance

### 13. Support and Updates

#### Getting Help:
- Check logs in `solver.log`
- Review screenshot analysis results
- Test with simple challenges first
- Ensure all dependencies are installed

#### Future Enhancements:
- Support for more challenge types
- Improved AI accuracy
- Better accessibility features
- Mobile device support
- Custom training capabilities

## Quick Start Checklist

- [ ] Install Python 3.8+
- [ ] Install dependencies: `pip install -r requirement.txt`
- [ ] Get Gemini API key
- [ ] Create `.env` file with API key
- [ ] Test with: `python solver.py`
- [ ] Choose option 1 to test current challenge
- [ ] Verify audio feedback works
- [ ] Check logs for any errors

## Example Workflow

1. **Open browser** to page with challenge
2. **Run solver**: `python solver.py`
3. **Choose option 1**: "Solve current challenge"
4. **Listen to audio**: Solver announces actions
5. **Wait for completion**: Challenge should be solved automatically
6. **Check result**: Audio feedback confirms success/failure

This system provides a comprehensive solution for visually impaired users to navigate web challenges independently and efficiently.
