#!/usr/bin/env python3
"""
Multi-Monitor Setup and Debugging Utility
Helps configure and test dual/multi-monitor setups for the captcha solver
"""

import os
import sys
import time
import mss
import pyautogui
from PIL import Image, ImageDraw, ImageFont
import tkinter as tk
from tkinter import messagebox

class MonitorSetupUtility:
    """Utility for setting up and testing multi-monitor configurations"""
    
    def __init__(self):
        self.sct = mss.mss()
        self.monitors = self.sct.monitors
        print(f"Detected {len(self.monitors)-1} monitors")
    
    def list_monitors(self):
        """List all available monitors with detailed information"""
        print("\n" + "="*60)
        print("MONITOR CONFIGURATION")
        print("="*60)
        
        for i, monitor in enumerate(self.monitors):
            if i == 0:
                print(f"All Monitors Combined: {monitor}")
            else:
                print(f"Monitor {i}:")
                print(f"  Resolution: {monitor['width']} x {monitor['height']}")
                print(f"  Position: ({monitor['left']}, {monitor['top']})")
                print(f"  Right edge: {monitor['left'] + monitor['width']}")
                print(f"  Bottom edge: {monitor['top'] + monitor['height']}")
                
                # Determine if this is likely the primary monitor
                if monitor['left'] == 0 and monitor['top'] == 0:
                    print(f"  Status: PRIMARY MONITOR")
                else:
                    print(f"  Status: Secondary monitor")
                print()
    
    def detect_mouse_monitor(self):
        """Detect which monitor the mouse cursor is currently on"""
        mouse_x, mouse_y = pyautogui.position()
        print(f"\nCurrent mouse position: ({mouse_x}, {mouse_y})")
        
        for i, monitor in enumerate(self.monitors[1:], 1):
            if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
                monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
                print(f"Mouse is on Monitor {i}")
                return i
        
        print("Mouse position could not be determined")
        return None
    
    def capture_monitor_preview(self, monitor_id: int, save_path: str = None):
        """Capture a preview of the specified monitor"""
        if monitor_id >= len(self.monitors):
            print(f"Monitor {monitor_id} not found")
            return None
        
        try:
            screenshot = self.sct.grab(self.monitors[monitor_id])
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
            
            # Add monitor info overlay
            draw = ImageDraw.Draw(img)
            monitor = self.monitors[monitor_id]
            info_text = f"Monitor {monitor_id}: {monitor['width']}x{monitor['height']} at ({monitor['left']}, {monitor['top']})"
            
            try:
                # Try to use a larger font
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            # Draw text with background
            text_bbox = draw.textbbox((0, 0), info_text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            draw.rectangle([10, 10, 10 + text_width + 20, 10 + text_height + 20], fill="black")
            draw.text((20, 20), info_text, fill="white", font=font)
            
            if save_path:
                img.save(save_path)
                print(f"Monitor {monitor_id} preview saved to: {save_path}")
            
            return img
            
        except Exception as e:
            print(f"Error capturing monitor {monitor_id}: {e}")
            return None
    
    def test_coordinate_translation(self, monitor_id: int, screenshot_x: int, screenshot_y: int):
        """Test coordinate translation for a specific monitor"""
        if monitor_id >= len(self.monitors):
            print(f"Monitor {monitor_id} not found")
            return None, None
        
        monitor = self.monitors[monitor_id]
        actual_x = screenshot_x + monitor['left']
        actual_y = screenshot_y + monitor['top']
        
        print(f"Monitor {monitor_id} coordinate translation:")
        print(f"  Screenshot coordinates: ({screenshot_x}, {screenshot_y})")
        print(f"  Monitor offset: ({monitor['left']}, {monitor['top']})")
        print(f"  Actual screen coordinates: ({actual_x}, {actual_y})")
        
        return actual_x, actual_y
    
    def visual_click_test(self, monitor_id: int, screenshot_x: int, screenshot_y: int):
        """Visually test clicking at translated coordinates"""
        actual_x, actual_y = self.test_coordinate_translation(monitor_id, screenshot_x, screenshot_y)
        
        if actual_x is None:
            return
        
        print(f"\nTesting click at translated coordinates...")
        print(f"Moving mouse to ({actual_x}, {actual_y}) in 3 seconds...")
        
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        # Move mouse to the position (don't click)
        pyautogui.moveTo(actual_x, actual_y, duration=1.0)
        print("Mouse moved to target position. Check if it's in the right place!")
        
        # Ask user if position is correct
        response = input("Is the mouse in the correct position? (y/n): ").lower()
        return response == 'y'
    
    def interactive_setup(self):
        """Interactive setup wizard for multi-monitor configuration"""
        print("\n" + "="*60)
        print("MULTI-MONITOR SETUP WIZARD")
        print("="*60)
        
        # Step 1: List monitors
        self.list_monitors()
        
        # Step 2: Detect current mouse position
        current_monitor = self.detect_mouse_monitor()
        
        # Step 3: Capture previews of all monitors
        print("\nCapturing monitor previews...")
        for i in range(1, len(self.monitors)):
            filename = f"monitor_{i}_preview.png"
            self.capture_monitor_preview(i, filename)
        
        # Step 4: Let user choose target monitor
        print(f"\nCurrent mouse is on monitor: {current_monitor}")
        target_monitor = input(f"Which monitor should be used for captcha solving? (1-{len(self.monitors)-1}): ")
        
        try:
            target_monitor = int(target_monitor)
            if 1 <= target_monitor < len(self.monitors):
                print(f"Selected monitor {target_monitor} for captcha solving")
                
                # Step 5: Test coordinate translation
                print("\nTesting coordinate translation...")
                test_x = int(input("Enter test X coordinate (screenshot coordinate): "))
                test_y = int(input("Enter test Y coordinate (screenshot coordinate): "))
                
                success = self.visual_click_test(target_monitor, test_x, test_y)
                
                if success:
                    print("✅ Coordinate translation working correctly!")
                    self.save_monitor_config(target_monitor)
                else:
                    print("❌ Coordinate translation needs adjustment")
                    print("Try running the setup again or check monitor configuration")
            else:
                print("Invalid monitor selection")
                
        except ValueError:
            print("Invalid input")
    
    def save_monitor_config(self, monitor_id: int):
        """Save monitor configuration to file"""
        config = {
            'active_monitor': monitor_id,
            'monitor_info': self.monitors[monitor_id],
            'total_monitors': len(self.monitors) - 1
        }
        
        try:
            import json
            with open('monitor_config.json', 'w') as f:
                json.dump(config, f, indent=2)
            print(f"Monitor configuration saved to monitor_config.json")
        except Exception as e:
            print(f"Error saving configuration: {e}")
    
    def load_monitor_config(self):
        """Load monitor configuration from file"""
        try:
            import json
            with open('monitor_config.json', 'r') as f:
                config = json.load(f)
            print("Loaded monitor configuration:")
            print(f"  Active monitor: {config['active_monitor']}")
            print(f"  Total monitors: {config['total_monitors']}")
            return config
        except FileNotFoundError:
            print("No monitor configuration file found")
            return None
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return None
    
    def create_test_pattern(self, monitor_id: int):
        """Create a test pattern on the specified monitor"""
        if monitor_id >= len(self.monitors):
            print(f"Monitor {monitor_id} not found")
            return
        
        monitor = self.monitors[monitor_id]
        
        # Create a simple GUI window on the target monitor
        root = tk.Tk()
        root.title(f"Monitor {monitor_id} Test Pattern")
        
        # Position window on target monitor
        root.geometry(f"400x300+{monitor['left']+100}+{monitor['top']+100}")
        
        # Create test elements
        tk.Label(root, text=f"Monitor {monitor_id} Test Pattern", font=("Arial", 16)).pack(pady=20)
        tk.Label(root, text=f"Resolution: {monitor['width']}x{monitor['height']}").pack()
        tk.Label(root, text=f"Position: ({monitor['left']}, {monitor['top']})").pack()
        
        # Test button at known coordinates
        test_button = tk.Button(root, text="Test Click Target", bg="red", fg="white", 
                               font=("Arial", 12), width=20, height=2)
        test_button.pack(pady=30)
        
        # Instructions
        tk.Label(root, text="Use this window to test coordinate translation", 
                wraplength=350).pack(pady=10)
        
        tk.Button(root, text="Close", command=root.destroy).pack()
        
        print(f"Test pattern window created on monitor {monitor_id}")
        print("Use the red button as a click target for testing")
        
        root.mainloop()

def main():
    """Main function for monitor setup utility"""
    utility = MonitorSetupUtility()
    
    print("Multi-Monitor Setup Utility for Captcha Solver")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. List all monitors")
        print("2. Detect mouse monitor")
        print("3. Capture monitor preview")
        print("4. Test coordinate translation")
        print("5. Visual click test")
        print("6. Interactive setup wizard")
        print("7. Create test pattern")
        print("8. Load saved configuration")
        print("9. Exit")
        
        choice = input("\nEnter choice (1-9): ").strip()
        
        try:
            if choice == "1":
                utility.list_monitors()
                
            elif choice == "2":
                utility.detect_mouse_monitor()
                
            elif choice == "3":
                monitor_id = int(input("Enter monitor number: "))
                filename = f"monitor_{monitor_id}_preview.png"
                utility.capture_monitor_preview(monitor_id, filename)
                
            elif choice == "4":
                monitor_id = int(input("Enter monitor number: "))
                x = int(input("Enter screenshot X coordinate: "))
                y = int(input("Enter screenshot Y coordinate: "))
                utility.test_coordinate_translation(monitor_id, x, y)
                
            elif choice == "5":
                monitor_id = int(input("Enter monitor number: "))
                x = int(input("Enter screenshot X coordinate: "))
                y = int(input("Enter screenshot Y coordinate: "))
                utility.visual_click_test(monitor_id, x, y)
                
            elif choice == "6":
                utility.interactive_setup()
                
            elif choice == "7":
                monitor_id = int(input("Enter monitor number: "))
                utility.create_test_pattern(monitor_id)
                
            elif choice == "8":
                utility.load_monitor_config()
                
            elif choice == "9":
                print("Exiting...")
                break
                
            else:
                print("Invalid choice")
                
        except ValueError:
            print("Invalid input. Please enter a number.")
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
