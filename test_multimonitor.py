#!/usr/bin/env python3
"""
Quick test script for multi-monitor functionality
Tests coordinate translation and monitor detection
"""

import os
import sys
import time
import pyautogui
from PIL import Image, ImageDraw

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_multimonitor():
    """Test basic multi-monitor detection"""
    print("Testing Multi-Monitor Detection")
    print("=" * 40)
    
    try:
        from solver import CaptchaSolver
        
        # Mock API key for testing
        api_key = "test_key_for_monitor_testing"
        
        # Initialize solver (will fail on Gemini but that's OK for monitor testing)
        try:
            solver = CaptchaSolver(api_key)
        except:
            # Create components individually for testing
            from solver import ScreenCapture, AutomationController
            screen_capture = ScreenCapture()
            automation = AutomationController(screen_capture)
            
            print(f"Detected {len(screen_capture.monitors)-1} monitors")
            print(f"Active monitor: {screen_capture.active_monitor}")
            print(f"Monitor offset: {screen_capture.monitor_offset}")
            
            # Test coordinate translation
            test_coords = [(100, 100), (500, 300), (800, 600)]
            
            print("\nTesting coordinate translation:")
            for screenshot_x, screenshot_y in test_coords:
                actual_x, actual_y = automation._translate_coordinates(screenshot_x, screenshot_y)
                print(f"  Screenshot ({screenshot_x}, {screenshot_y}) -> Actual ({actual_x}, {actual_y})")
            
            return True
            
    except Exception as e:
        print(f"Error during multi-monitor test: {e}")
        return False

def test_monitor_capture():
    """Test capturing from different monitors"""
    print("\nTesting Monitor Capture")
    print("=" * 40)
    
    try:
        from solver import ScreenCapture
        screen_capture = ScreenCapture()
        
        # Capture from each monitor
        for i in range(1, len(screen_capture.monitors)):
            print(f"Capturing from monitor {i}...")
            
            try:
                screenshot = screen_capture.capture_screen(monitor_id=i)
                filename = f"test_monitor_{i}.png"
                screen_capture.save_screenshot(screenshot, filename)
                print(f"  ✅ Monitor {i} captured successfully -> {filename}")
            except Exception as e:
                print(f"  ❌ Monitor {i} capture failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"Error during monitor capture test: {e}")
        return False

def test_mouse_detection():
    """Test mouse position detection across monitors"""
    print("\nTesting Mouse Detection")
    print("=" * 40)
    
    try:
        from solver import ScreenCapture
        screen_capture = ScreenCapture()
        
        print("Move your mouse to different monitors and press Enter...")
        print("Type 'quit' to stop testing")
        
        while True:
            user_input = input("Press Enter to detect mouse position (or 'quit'): ")
            if user_input.lower() == 'quit':
                break
            
            mouse_x, mouse_y = pyautogui.position()
            print(f"Mouse position: ({mouse_x}, {mouse_y})")
            
            # Check which monitor
            detected_monitor = screen_capture._detect_active_monitor()
            print(f"Detected on monitor: {detected_monitor}")
            
            # Show monitor bounds
            if detected_monitor < len(screen_capture.monitors):
                monitor = screen_capture.monitors[detected_monitor]
                print(f"Monitor bounds: ({monitor['left']}, {monitor['top']}) to "
                      f"({monitor['left'] + monitor['width']}, {monitor['top'] + monitor['height']})")
        
        return True
        
    except Exception as e:
        print(f"Error during mouse detection test: {e}")
        return False

def test_coordinate_clicking():
    """Test coordinate clicking with visual feedback"""
    print("\nTesting Coordinate Clicking")
    print("=" * 40)
    
    try:
        from solver import ScreenCapture, AutomationController, ClickCoordinate
        
        screen_capture = ScreenCapture()
        automation = AutomationController(screen_capture)
        
        print("This test will move the mouse to test coordinates")
        print("Watch the mouse movement to verify correct positioning")
        
        # Test coordinates (screenshot coordinates)
        test_coords = [
            (200, 200, "Top-left area"),
            (500, 300, "Center area"),
            (800, 400, "Right area")
        ]
        
        for x, y, description in test_coords:
            print(f"\nTesting {description} at screenshot coords ({x}, {y})")
            
            # Create click coordinate
            coord = ClickCoordinate(
                x=x, y=y,
                confidence=1.0,
                description=description,
                element_type="test"
            )
            
            # Translate coordinates
            actual_x, actual_y = automation._translate_coordinates(x, y)
            print(f"Translated to actual coords: ({actual_x}, {actual_y})")
            
            # Move mouse (don't click)
            print("Moving mouse in 2 seconds...")
            time.sleep(2)
            pyautogui.moveTo(actual_x, actual_y, duration=1.0)
            
            input("Press Enter to continue to next coordinate...")
        
        return True
        
    except Exception as e:
        print(f"Error during coordinate clicking test: {e}")
        return False

def create_visual_test_targets():
    """Create visual test targets on each monitor"""
    print("\nCreating Visual Test Targets")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from solver import ScreenCapture
        
        screen_capture = ScreenCapture()
        
        # Create test windows on each monitor
        for i in range(1, len(screen_capture.monitors)):
            monitor = screen_capture.monitors[i]
            
            root = tk.Tk()
            root.title(f"Monitor {i} Test Target")
            root.geometry(f"300x200+{monitor['left']+50}+{monitor['top']+50}")
            root.configure(bg='lightblue')
            
            # Add labels
            tk.Label(root, text=f"Monitor {i}", font=("Arial", 16), bg='lightblue').pack(pady=10)
            tk.Label(root, text=f"Resolution: {monitor['width']}x{monitor['height']}", bg='lightblue').pack()
            tk.Label(root, text=f"Offset: ({monitor['left']}, {monitor['top']})", bg='lightblue').pack()
            
            # Add test button
            test_btn = tk.Button(root, text="Click Test Target", bg='red', fg='white',
                               font=("Arial", 12), width=15, height=2)
            test_btn.pack(pady=20)
            
            # Add close button
            tk.Button(root, text="Close", command=root.destroy).pack()
            
            print(f"Created test window on monitor {i}")
        
        print("Test windows created. Use these as click targets for testing.")
        print("Close the windows when done testing.")
        
        return True
        
    except Exception as e:
        print(f"Error creating visual test targets: {e}")
        return False

def main():
    """Main test function"""
    print("Multi-Monitor Functionality Test")
    print("=" * 50)
    
    tests = [
        ("Basic Multi-Monitor Detection", test_basic_multimonitor),
        ("Monitor Capture", test_monitor_capture),
        ("Mouse Detection", test_mouse_detection),
        ("Coordinate Clicking", test_coordinate_clicking),
        ("Visual Test Targets", create_visual_test_targets)
    ]
    
    print("Available tests:")
    for i, (name, _) in enumerate(tests, 1):
        print(f"{i}. {name}")
    print("6. Run all tests")
    print("7. Exit")
    
    while True:
        try:
            choice = input("\nEnter test number (1-7): ").strip()
            
            if choice == "7":
                print("Exiting...")
                break
            elif choice == "6":
                print("Running all tests...")
                for name, test_func in tests:
                    print(f"\n{'='*20} {name} {'='*20}")
                    try:
                        success = test_func()
                        print(f"✅ {name}: {'PASSED' if success else 'FAILED'}")
                    except Exception as e:
                        print(f"❌ {name}: ERROR - {e}")
                    print()
            elif choice in ["1", "2", "3", "4", "5"]:
                test_index = int(choice) - 1
                name, test_func = tests[test_index]
                print(f"\n{'='*20} {name} {'='*20}")
                try:
                    success = test_func()
                    print(f"✅ {name}: {'PASSED' if success else 'FAILED'}")
                except Exception as e:
                    print(f"❌ {name}: ERROR - {e}")
            else:
                print("Invalid choice")
                
        except KeyboardInterrupt:
            print("\nTest interrupted by user")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
