#!/usr/bin/env python3
"""
Fast Coordinate Accuracy Benchmark
Ultra-fast testing and optimization of coordinate accuracy
"""

import os
import sys
import time
import json
import math
import pyautogui
import mss
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from concurrent.futures import ThreadPoolExecutor
import threading

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class FastAccuracyBenchmark:
    """Ultra-fast coordinate accuracy testing and optimization"""
    
    def __init__(self):
        self.sct = mss.mss()
        self.monitors = self.sct.monitors
        self.active_monitor = self._detect_active_monitor()
        self.benchmark_results = []
        self.optimization_data = {}
        
        # Performance settings
        self.fast_mode = True
        self.parallel_testing = True
        self.test_iterations = 50
        
        print(f"🚀 Fast Accuracy Benchmark initialized on monitor {self.active_monitor}")
    
    def _detect_active_monitor(self) -> int:
        """Detect active monitor"""
        mouse_x, mouse_y = pyautogui.position()
        for i, monitor in enumerate(self.monitors[1:], 1):
            if (monitor['left'] <= mouse_x < monitor['left'] + monitor['width'] and
                monitor['top'] <= mouse_y < monitor['top'] + monitor['height']):
                return i
        return 1
    
    def run_ultra_fast_benchmark(self) -> Dict:
        """Run ultra-fast coordinate accuracy benchmark"""
        print("🏃‍♂️ Running Ultra-Fast Coordinate Benchmark")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test 1: Basic accuracy test (parallel)
        basic_results = self._parallel_basic_accuracy_test()
        
        # Test 2: Speed test
        speed_results = self._speed_accuracy_test()
        
        # Test 3: Edge case test
        edge_results = self._edge_case_test()
        
        # Test 4: Precision test
        precision_results = self._precision_test()
        
        total_time = time.time() - start_time
        
        # Compile results
        benchmark_results = {
            'total_time': total_time,
            'basic_accuracy': basic_results,
            'speed_performance': speed_results,
            'edge_cases': edge_results,
            'precision': precision_results,
            'overall_score': self._calculate_overall_score(basic_results, speed_results, edge_results, precision_results)
        }
        
        # Generate optimization recommendations
        optimization_recommendations = self._generate_optimization_recommendations(benchmark_results)
        benchmark_results['recommendations'] = optimization_recommendations
        
        # Save results
        self._save_benchmark_results(benchmark_results)
        
        # Display results
        self._display_results(benchmark_results)
        
        return benchmark_results
    
    def _parallel_basic_accuracy_test(self) -> Dict:
        """Parallel basic accuracy testing"""
        print("📐 Basic Accuracy Test (Parallel)")
        
        monitor = self.monitors[self.active_monitor]
        test_points = self._generate_test_points(monitor, 20)
        
        if self.parallel_testing:
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = [executor.submit(self._test_single_coordinate, point) 
                          for point in test_points]
                
                results = []
                for future in futures:
                    try:
                        result = future.result(timeout=2)
                        results.append(result)
                    except Exception as e:
                        print(f"Test failed: {e}")
        else:
            results = [self._test_single_coordinate(point) for point in test_points]
        
        # Calculate statistics
        errors = [r['error'] for r in results if r]
        if not errors:
            return {'average_error': 999, 'max_error': 999, 'accuracy_rate': 0}
        
        return {
            'average_error': sum(errors) / len(errors),
            'max_error': max(errors),
            'min_error': min(errors),
            'accuracy_rate': len([e for e in errors if e < 5]) / len(errors),
            'test_count': len(results)
        }
    
    def _speed_accuracy_test(self) -> Dict:
        """Test speed vs accuracy trade-off"""
        print("⚡ Speed vs Accuracy Test")
        
        monitor = self.monitors[self.active_monitor]
        test_points = self._generate_test_points(monitor, 10)
        
        speed_tests = [
            {'duration': 0.1, 'name': 'Ultra Fast'},
            {'duration': 0.2, 'name': 'Fast'},
            {'duration': 0.5, 'name': 'Normal'},
            {'duration': 1.0, 'name': 'Slow'}
        ]
        
        speed_results = {}
        
        for speed_test in speed_tests:
            start_time = time.time()
            errors = []
            
            for point in test_points:
                screenshot_x, screenshot_y = point
                expected_x = screenshot_x + monitor['left']
                expected_y = screenshot_y + monitor['top']
                
                # Move with specified duration
                pyautogui.moveTo(expected_x, expected_y, duration=speed_test['duration'])
                actual_x, actual_y = pyautogui.position()
                
                error = math.sqrt((actual_x - expected_x)**2 + (actual_y - expected_y)**2)
                errors.append(error)
            
            test_time = time.time() - start_time
            
            speed_results[speed_test['name']] = {
                'average_error': sum(errors) / len(errors),
                'total_time': test_time,
                'points_per_second': len(test_points) / test_time
            }
        
        return speed_results
    
    def _edge_case_test(self) -> Dict:
        """Test edge cases and boundary conditions"""
        print("🔍 Edge Case Test")
        
        monitor = self.monitors[self.active_monitor]
        
        edge_cases = [
            (0, 0, "Origin"),
            (1, 1, "Near Origin"),
            (monitor['width']-1, monitor['height']-1, "Max Bounds"),
            (monitor['width']-2, monitor['height']-2, "Near Max"),
            (monitor['width']//2, 0, "Top Center"),
            (monitor['width']//2, monitor['height']-1, "Bottom Center"),
            (0, monitor['height']//2, "Left Center"),
            (monitor['width']-1, monitor['height']//2, "Right Center")
        ]
        
        edge_results = []
        
        for screenshot_x, screenshot_y, description in edge_cases:
            expected_x = screenshot_x + monitor['left']
            expected_y = screenshot_y + monitor['top']
            
            # Validate bounds
            screen_width, screen_height = pyautogui.size()
            if not (0 <= expected_x < screen_width and 0 <= expected_y < screen_height):
                edge_results.append({
                    'description': description,
                    'error': 999,
                    'passed': False,
                    'reason': 'Out of bounds'
                })
                continue
            
            try:
                pyautogui.moveTo(expected_x, expected_y, duration=0.3)
                actual_x, actual_y = pyautogui.position()
                
                error = math.sqrt((actual_x - expected_x)**2 + (actual_y - expected_y)**2)
                passed = error < 10
                
                edge_results.append({
                    'description': description,
                    'error': error,
                    'passed': passed,
                    'reason': 'Success' if passed else f'High error: {error:.1f}px'
                })
                
            except Exception as e:
                edge_results.append({
                    'description': description,
                    'error': 999,
                    'passed': False,
                    'reason': str(e)
                })
        
        pass_rate = len([r for r in edge_results if r['passed']]) / len(edge_results)
        
        return {
            'results': edge_results,
            'pass_rate': pass_rate,
            'total_tests': len(edge_results)
        }
    
    def _precision_test(self) -> Dict:
        """Test sub-pixel precision"""
        print("🎯 Precision Test")
        
        monitor = self.monitors[self.active_monitor]
        
        # Test fractional coordinates
        precision_points = [
            (100.5, 100.5),
            (200.3, 300.7),
            (500.1, 400.9),
            (300.8, 250.2)
        ]
        
        precision_results = []
        
        for float_x, float_y in precision_points:
            expected_x = float_x + monitor['left']
            expected_y = float_y + monitor['top']
            
            # Test rounding strategies
            strategies = [
                ('round', round(expected_x), round(expected_y)),
                ('floor', int(expected_x), int(expected_y)),
                ('ceil', math.ceil(expected_x), math.ceil(expected_y))
            ]
            
            strategy_results = {}
            
            for strategy_name, int_x, int_y in strategies:
                pyautogui.moveTo(int_x, int_y, duration=0.2)
                actual_x, actual_y = pyautogui.position()
                
                error = math.sqrt((actual_x - expected_x)**2 + (actual_y - expected_y)**2)
                strategy_results[strategy_name] = error
            
            best_strategy = min(strategy_results, key=strategy_results.get)
            precision_results.append({
                'target': (float_x, float_y),
                'strategies': strategy_results,
                'best_strategy': best_strategy,
                'best_error': strategy_results[best_strategy]
            })
        
        avg_precision = sum(r['best_error'] for r in precision_results) / len(precision_results)
        
        return {
            'results': precision_results,
            'average_precision': avg_precision,
            'recommended_strategy': self._get_recommended_strategy(precision_results)
        }
    
    def _test_single_coordinate(self, point: Tuple[int, int]) -> Dict:
        """Test a single coordinate"""
        try:
            screenshot_x, screenshot_y = point
            monitor = self.monitors[self.active_monitor]
            
            expected_x = screenshot_x + monitor['left']
            expected_y = screenshot_y + monitor['top']
            
            # Move mouse
            pyautogui.moveTo(expected_x, expected_y, duration=0.2)
            time.sleep(0.05)
            
            # Get actual position
            actual_x, actual_y = pyautogui.position()
            
            # Calculate error
            error = math.sqrt((actual_x - expected_x)**2 + (actual_y - expected_y)**2)
            
            return {
                'screenshot_coords': (screenshot_x, screenshot_y),
                'expected_coords': (expected_x, expected_y),
                'actual_coords': (actual_x, actual_y),
                'error': error,
                'success': error < 5
            }
            
        except Exception as e:
            return {
                'error': 999,
                'success': False,
                'exception': str(e)
            }
    
    def _generate_test_points(self, monitor: Dict, count: int) -> List[Tuple[int, int]]:
        """Generate test points for the monitor"""
        points = []
        
        # Grid points
        grid_size = int(math.sqrt(count))
        for i in range(grid_size):
            for j in range(grid_size):
                x = (monitor['width'] * (i + 1)) // (grid_size + 1)
                y = (monitor['height'] * (j + 1)) // (grid_size + 1)
                points.append((x, y))
        
        # Add some random points
        import random
        while len(points) < count:
            x = random.randint(50, monitor['width'] - 50)
            y = random.randint(50, monitor['height'] - 50)
            points.append((x, y))
        
        return points[:count]
    
    def _calculate_overall_score(self, basic: Dict, speed: Dict, edge: Dict, precision: Dict) -> float:
        """Calculate overall accuracy score"""
        # Basic accuracy (40 points)
        basic_score = max(0, 40 - basic['average_error'] * 4)
        
        # Speed performance (20 points)
        fast_error = speed.get('Fast', {}).get('average_error', 10)
        speed_score = max(0, 20 - fast_error * 2)
        
        # Edge cases (20 points)
        edge_score = edge['pass_rate'] * 20
        
        # Precision (20 points)
        precision_score = max(0, 20 - precision['average_precision'] * 4)
        
        return basic_score + speed_score + edge_score + precision_score
    
    def _generate_optimization_recommendations(self, results: Dict) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []
        
        basic = results['basic_accuracy']
        if basic['average_error'] > 5:
            recommendations.append("🔧 High average error detected. Consider display scaling adjustment.")
        
        if basic['accuracy_rate'] < 0.8:
            recommendations.append("⚠️  Low accuracy rate. Check monitor configuration.")
        
        speed = results['speed_performance']
        if 'Fast' in speed and speed['Fast']['average_error'] > 3:
            recommendations.append("🚀 Fast movement accuracy needs improvement. Use slower movements.")
        
        edge = results['edge_cases']
        if edge['pass_rate'] < 0.7:
            recommendations.append("📐 Edge case failures detected. Check screen bounds handling.")
        
        precision = results['precision']
        if precision['average_precision'] > 2:
            recommendations.append("🎯 Sub-pixel precision issues. Implement better rounding strategy.")
        
        if not recommendations:
            recommendations.append("✅ Excellent performance! No optimizations needed.")
        
        return recommendations
    
    def _get_recommended_strategy(self, precision_results: List[Dict]) -> str:
        """Get recommended rounding strategy"""
        strategy_scores = {'round': 0, 'floor': 0, 'ceil': 0}
        
        for result in precision_results:
            best = result['best_strategy']
            strategy_scores[best] += 1
        
        return max(strategy_scores, key=strategy_scores.get)
    
    def _save_benchmark_results(self, results: Dict):
        """Save benchmark results"""
        timestamp = int(time.time())
        filename = f"fast_benchmark_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📄 Results saved: {filename}")
    
    def _display_results(self, results: Dict):
        """Display benchmark results"""
        print(f"\n🏆 FAST ACCURACY BENCHMARK RESULTS")
        print("=" * 60)
        
        print(f"⏱️  Total Time: {results['total_time']:.2f}s")
        print(f"🎯 Overall Score: {results['overall_score']:.1f}/100")
        
        basic = results['basic_accuracy']
        print(f"\n📐 Basic Accuracy:")
        print(f"   Average Error: {basic['average_error']:.1f}px")
        print(f"   Accuracy Rate: {basic['accuracy_rate']:.1%}")
        
        speed = results['speed_performance']
        print(f"\n⚡ Speed Performance:")
        for name, data in speed.items():
            print(f"   {name}: {data['average_error']:.1f}px error, {data['points_per_second']:.1f} pts/s")
        
        edge = results['edge_cases']
        print(f"\n🔍 Edge Cases: {edge['pass_rate']:.1%} pass rate")
        
        precision = results['precision']
        print(f"\n🎯 Precision: {precision['average_precision']:.1f}px average")
        print(f"   Recommended strategy: {precision['recommended_strategy']}")
        
        print(f"\n💡 Recommendations:")
        for rec in results['recommendations']:
            print(f"   {rec}")

def main():
    """Main benchmark function"""
    benchmark = FastAccuracyBenchmark()
    
    print("🚀 Fast Coordinate Accuracy Benchmark")
    print("This will test coordinate accuracy quickly and provide optimization recommendations.")
    
    input("Press Enter to start benchmark...")
    
    results = benchmark.run_ultra_fast_benchmark()
    
    print(f"\n✅ Benchmark completed!")
    print(f"Overall Score: {results['overall_score']:.1f}/100")
    
    if results['overall_score'] >= 80:
        print("🎉 Excellent accuracy! Your system is ready for production.")
    elif results['overall_score'] >= 60:
        print("👍 Good accuracy. Minor optimizations recommended.")
    else:
        print("⚠️  Accuracy needs improvement. Follow the recommendations.")

if __name__ == "__main__":
    main()
