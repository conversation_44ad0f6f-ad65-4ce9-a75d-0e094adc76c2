#!/usr/bin/env python3
"""
Advanced Coordinate Engine
Implements cutting-edge techniques for pixel-perfect coordinate accuracy
"""

import os
import sys
import time
import json
import math
import cv2
import numpy as np
import pyautogui
import mss
from PIL import Image, ImageDraw, ImageFont
from sklearn.linear_model import LinearRegression
from scipy import ndimage
import threading
from queue import Queue

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class AdvancedCoordinateEngine:
    """Ultra-precise coordinate mapping with machine learning and computer vision"""
    
    def __init__(self):
        self.sct = mss.mss()
        self.monitors = self.sct.monitors
        self.active_monitor = 1
        
        # Advanced calibration data
        self.calibration_matrix = None
        self.error_correction_model = None
        self.template_cache = {}
        self.coordinate_history = []
        
        # Performance optimization
        self.fast_mode = True
        self.cache_enabled = True
        
        # Computer vision settings
        self.template_matching_threshold = 0.8
        self.edge_detection_threshold = 50
        
        print("🚀 Advanced Coordinate Engine initialized")
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize machine learning models for coordinate correction"""
        try:
            # Try to load existing calibration data
            if os.path.exists("advanced_calibration.json"):
                with open("advanced_calibration.json", 'r') as f:
                    data = json.load(f)
                    self.coordinate_history = data.get('coordinate_history', [])
                    print(f"✅ Loaded {len(self.coordinate_history)} calibration points")
            
            # Initialize error correction model if we have enough data
            if len(self.coordinate_history) >= 10:
                self._train_error_correction_model()
                
        except Exception as e:
            print(f"⚠️  Could not load calibration data: {e}")
    
    def _train_error_correction_model(self):
        """Train machine learning model for coordinate error correction"""
        if len(self.coordinate_history) < 10:
            return
        
        try:
            # Prepare training data
            X = []  # [screenshot_x, screenshot_y, monitor_offset_x, monitor_offset_y]
            y_x = []  # actual error in X
            y_y = []  # actual error in Y
            
            for record in self.coordinate_history:
                X.append([
                    record['screenshot_x'],
                    record['screenshot_y'],
                    record['monitor_offset_x'],
                    record['monitor_offset_y']
                ])
                y_x.append(record['error_x'])
                y_y.append(record['error_y'])
            
            X = np.array(X)
            y_x = np.array(y_x)
            y_y = np.array(y_y)
            
            # Train separate models for X and Y corrections
            self.error_correction_model = {
                'x_model': LinearRegression().fit(X, y_x),
                'y_model': LinearRegression().fit(X, y_y)
            }
            
            print(f"✅ Trained error correction model with {len(X)} samples")
            
        except Exception as e:
            print(f"❌ Error training model: {e}")
    
    def detect_elements_with_cv(self, image, element_types=['button', 'checkbox']):
        """Use computer vision to detect clickable elements"""
        try:
            # Convert PIL to OpenCV
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            detected_elements = []
            
            # Method 1: Edge detection for buttons
            if 'button' in element_types:
                button_elements = self._detect_buttons_cv(gray, cv_image)
                detected_elements.extend(button_elements)
            
            # Method 2: Template matching for checkboxes
            if 'checkbox' in element_types:
                checkbox_elements = self._detect_checkboxes_cv(gray, cv_image)
                detected_elements.extend(checkbox_elements)
            
            # Method 3: Contour detection for general elements
            contour_elements = self._detect_contours_cv(gray, cv_image)
            detected_elements.extend(contour_elements)
            
            return detected_elements
            
        except Exception as e:
            print(f"❌ CV detection error: {e}")
            return []
    
    def _detect_buttons_cv(self, gray, color_image):
        """Detect buttons using edge detection and contour analysis"""
        elements = []
        
        try:
            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Edge detection
            edges = cv2.Canny(blurred, self.edge_detection_threshold, 
                             self.edge_detection_threshold * 2)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                # Filter by area and aspect ratio (typical button characteristics)
                area = cv2.contourArea(contour)
                if 500 < area < 50000:  # Reasonable button size
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    
                    # Buttons typically have aspect ratio between 1:3 and 5:1
                    if 0.3 <= aspect_ratio <= 5.0:
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        elements.append({
                            'type': 'button',
                            'center': (center_x, center_y),
                            'bounds': (x, y, w, h),
                            'confidence': min(0.9, area / 10000),
                            'method': 'edge_detection'
                        })
            
        except Exception as e:
            print(f"Button detection error: {e}")
        
        return elements
    
    def _detect_checkboxes_cv(self, gray, color_image):
        """Detect checkboxes using template matching and shape analysis"""
        elements = []
        
        try:
            # Create checkbox templates
            checkbox_templates = self._generate_checkbox_templates()
            
            for template_name, template in checkbox_templates.items():
                # Template matching
                result = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
                locations = np.where(result >= self.template_matching_threshold)
                
                for pt in zip(*locations[::-1]):
                    x, y = pt
                    h, w = template.shape
                    center_x = x + w // 2
                    center_y = y + h // 2
                    
                    elements.append({
                        'type': 'checkbox',
                        'center': (center_x, center_y),
                        'bounds': (x, y, w, h),
                        'confidence': result[y, x],
                        'method': f'template_matching_{template_name}'
                    })
            
        except Exception as e:
            print(f"Checkbox detection error: {e}")
        
        return elements
    
    def _detect_contours_cv(self, gray, color_image):
        """Detect general clickable elements using contour analysis"""
        elements = []
        
        try:
            # Adaptive thresholding
            thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                         cv2.THRESH_BINARY, 11, 2)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 100 < area < 10000:  # Filter by reasonable size
                    # Approximate contour
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    # Look for rectangular shapes (4 corners)
                    if len(approx) == 4:
                        x, y, w, h = cv2.boundingRect(contour)
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        elements.append({
                            'type': 'rectangular_element',
                            'center': (center_x, center_y),
                            'bounds': (x, y, w, h),
                            'confidence': 0.7,
                            'method': 'contour_analysis'
                        })
            
        except Exception as e:
            print(f"Contour detection error: {e}")
        
        return elements
    
    def _generate_checkbox_templates(self):
        """Generate checkbox templates for template matching"""
        templates = {}
        
        try:
            # Empty checkbox template
            empty_checkbox = np.zeros((20, 20), dtype=np.uint8)
            cv2.rectangle(empty_checkbox, (2, 2), (17, 17), 255, 2)
            templates['empty'] = empty_checkbox
            
            # Checked checkbox template
            checked_checkbox = empty_checkbox.copy()
            cv2.line(checked_checkbox, (5, 10), (9, 14), 255, 2)
            cv2.line(checked_checkbox, (9, 14), (15, 6), 255, 2)
            templates['checked'] = checked_checkbox
            
            # Round checkbox template
            round_checkbox = np.zeros((20, 20), dtype=np.uint8)
            cv2.circle(round_checkbox, (10, 10), 8, 255, 2)
            templates['round'] = round_checkbox
            
        except Exception as e:
            print(f"Template generation error: {e}")
        
        return templates
    
    def ultra_precise_coordinate_mapping(self, screenshot_x, screenshot_y, monitor_info):
        """Ultra-precise coordinate mapping using multiple techniques"""
        
        # Step 1: Basic translation
        basic_x = screenshot_x + monitor_info['left']
        basic_y = screenshot_y + monitor_info['top']
        
        # Step 2: Apply machine learning correction
        ml_corrected_x, ml_corrected_y = self._apply_ml_correction(
            screenshot_x, screenshot_y, monitor_info['left'], monitor_info['top']
        )
        
        # Step 3: Apply sub-pixel precision
        precise_x, precise_y = self._apply_subpixel_precision(
            ml_corrected_x, ml_corrected_y
        )
        
        # Step 4: Apply dynamic calibration
        final_x, final_y = self._apply_dynamic_calibration(
            precise_x, precise_y, screenshot_x, screenshot_y
        )
        
        return int(round(final_x)), int(round(final_y))
    
    def _apply_ml_correction(self, screenshot_x, screenshot_y, offset_x, offset_y):
        """Apply machine learning-based error correction"""
        if not self.error_correction_model:
            return screenshot_x + offset_x, screenshot_y + offset_y
        
        try:
            # Prepare input features
            features = np.array([[screenshot_x, screenshot_y, offset_x, offset_y]])
            
            # Predict corrections
            error_x = self.error_correction_model['x_model'].predict(features)[0]
            error_y = self.error_correction_model['y_model'].predict(features)[0]
            
            # Apply corrections
            corrected_x = screenshot_x + offset_x - error_x
            corrected_y = screenshot_y + offset_y - error_y
            
            return corrected_x, corrected_y
            
        except Exception as e:
            print(f"ML correction error: {e}")
            return screenshot_x + offset_x, screenshot_y + offset_y
    
    def _apply_subpixel_precision(self, x, y):
        """Apply sub-pixel precision using interpolation"""
        try:
            # Use bilinear interpolation for sub-pixel accuracy
            # This helps with fractional pixel positions
            
            # Calculate fractional parts
            frac_x = x - int(x)
            frac_y = y - int(y)
            
            # Apply sub-pixel correction based on fractional position
            if frac_x > 0.5:
                x = math.ceil(x)
            else:
                x = math.floor(x)
            
            if frac_y > 0.5:
                y = math.ceil(y)
            else:
                y = math.floor(y)
            
            return x, y
            
        except Exception as e:
            print(f"Sub-pixel precision error: {e}")
            return x, y
    
    def _apply_dynamic_calibration(self, x, y, original_screenshot_x, original_screenshot_y):
        """Apply dynamic calibration based on screen region"""
        try:
            # Different screen regions may have different accuracy characteristics
            # Apply region-specific corrections
            
            screen_width, screen_height = pyautogui.size()
            
            # Normalize coordinates to 0-1 range
            norm_x = original_screenshot_x / screen_width
            norm_y = original_screenshot_y / screen_height
            
            # Apply region-specific corrections
            if norm_x < 0.1 or norm_x > 0.9:  # Edge regions
                x += 0.5 if norm_x < 0.1 else -0.5
            
            if norm_y < 0.1 or norm_y > 0.9:  # Top/bottom regions
                y += 0.5 if norm_y < 0.1 else -0.5
            
            return x, y
            
        except Exception as e:
            print(f"Dynamic calibration error: {e}")
            return x, y
    
    def fast_coordinate_detection(self, image, target_elements=['button', 'checkbox']):
        """Fast coordinate detection using optimized algorithms"""
        
        if not self.fast_mode:
            return self.detect_elements_with_cv(image, target_elements)
        
        # Use multi-threading for faster processing
        detection_queue = Queue()
        threads = []
        
        # Thread 1: Edge detection
        thread1 = threading.Thread(target=self._fast_edge_detection, 
                                  args=(image, detection_queue))
        threads.append(thread1)
        
        # Thread 2: Template matching
        thread2 = threading.Thread(target=self._fast_template_matching, 
                                  args=(image, detection_queue))
        threads.append(thread2)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Collect results
        all_elements = []
        while not detection_queue.empty():
            elements = detection_queue.get()
            all_elements.extend(elements)
        
        # Remove duplicates and rank by confidence
        unique_elements = self._remove_duplicate_elements(all_elements)
        ranked_elements = sorted(unique_elements, key=lambda x: x['confidence'], reverse=True)
        
        return ranked_elements[:5]  # Return top 5 candidates
    
    def _fast_edge_detection(self, image, result_queue):
        """Fast edge detection in separate thread"""
        try:
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Fast edge detection
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            elements = []
            for contour in contours[:10]:  # Limit to top 10 for speed
                area = cv2.contourArea(contour)
                if 200 < area < 20000:
                    x, y, w, h = cv2.boundingRect(contour)
                    center_x = x + w // 2
                    center_y = y + h // 2
                    
                    elements.append({
                        'type': 'edge_detected',
                        'center': (center_x, center_y),
                        'bounds': (x, y, w, h),
                        'confidence': min(0.8, area / 5000),
                        'method': 'fast_edge'
                    })
            
            result_queue.put(elements)
            
        except Exception as e:
            print(f"Fast edge detection error: {e}")
            result_queue.put([])
    
    def _fast_template_matching(self, image, result_queue):
        """Fast template matching in separate thread"""
        try:
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Use cached templates for speed
            if 'checkbox_template' not in self.template_cache:
                template = np.zeros((16, 16), dtype=np.uint8)
                cv2.rectangle(template, (2, 2), (13, 13), 255, 2)
                self.template_cache['checkbox_template'] = template
            
            template = self.template_cache['checkbox_template']
            result = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
            
            # Find peaks
            locations = np.where(result >= 0.6)
            elements = []
            
            for pt in zip(*locations[::-1]):
                if len(elements) >= 5:  # Limit for speed
                    break
                    
                x, y = pt
                h, w = template.shape
                center_x = x + w // 2
                center_y = y + h // 2
                
                elements.append({
                    'type': 'template_matched',
                    'center': (center_x, center_y),
                    'bounds': (x, y, w, h),
                    'confidence': result[y, x],
                    'method': 'fast_template'
                })
            
            result_queue.put(elements)
            
        except Exception as e:
            print(f"Fast template matching error: {e}")
            result_queue.put([])
    
    def _remove_duplicate_elements(self, elements):
        """Remove duplicate elements based on proximity"""
        if not elements:
            return []
        
        unique_elements = []
        proximity_threshold = 20  # pixels
        
        for element in elements:
            is_duplicate = False
            for unique_element in unique_elements:
                distance = math.sqrt(
                    (element['center'][0] - unique_element['center'][0])**2 +
                    (element['center'][1] - unique_element['center'][1])**2
                )
                
                if distance < proximity_threshold:
                    # Keep the one with higher confidence
                    if element['confidence'] > unique_element['confidence']:
                        unique_elements.remove(unique_element)
                        unique_elements.append(element)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_elements.append(element)
        
        return unique_elements
    
    def record_coordinate_accuracy(self, screenshot_x, screenshot_y, expected_x, expected_y, actual_x, actual_y, monitor_offset_x, monitor_offset_y):
        """Record coordinate accuracy for machine learning"""
        
        error_x = actual_x - expected_x
        error_y = actual_y - expected_y
        
        record = {
            'timestamp': time.time(),
            'screenshot_x': screenshot_x,
            'screenshot_y': screenshot_y,
            'expected_x': expected_x,
            'expected_y': expected_y,
            'actual_x': actual_x,
            'actual_y': actual_y,
            'error_x': error_x,
            'error_y': error_y,
            'monitor_offset_x': monitor_offset_x,
            'monitor_offset_y': monitor_offset_y
        }
        
        self.coordinate_history.append(record)
        
        # Retrain model periodically
        if len(self.coordinate_history) % 10 == 0:
            self._train_error_correction_model()
        
        # Save data periodically
        if len(self.coordinate_history) % 20 == 0:
            self.save_calibration_data()
    
    def save_calibration_data(self):
        """Save calibration data to file"""
        try:
            data = {
                'coordinate_history': self.coordinate_history,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_records': len(self.coordinate_history)
            }
            
            with open('advanced_calibration.json', 'w') as f:
                json.dump(data, f, indent=2)
            
            print(f"✅ Saved {len(self.coordinate_history)} calibration records")
            
        except Exception as e:
            print(f"❌ Error saving calibration data: {e}")

def main():
    """Test the advanced coordinate engine"""
    engine = AdvancedCoordinateEngine()
    
    print("🚀 Testing Advanced Coordinate Engine")
    print("=" * 50)
    
    # Test coordinate mapping
    test_coords = [(100, 100), (500, 300), (800, 600)]
    monitor_info = engine.monitors[1]
    
    for screenshot_x, screenshot_y in test_coords:
        precise_x, precise_y = engine.ultra_precise_coordinate_mapping(
            screenshot_x, screenshot_y, monitor_info
        )
        
        print(f"Screenshot ({screenshot_x}, {screenshot_y}) -> Precise ({precise_x}, {precise_y})")

if __name__ == "__main__":
    main()
