# AccessiCAPTCHA Solver

![Project Preview](https://placehold.co/1200x600?text=AccessiCAPTCHA+Demo)

**A coordinate-based AI assistant to help visually impaired employees solve reCAPTCHA and Cloudflare challenges**

## 📖 Table of Contents
- [Features](#-features)
- [Installation](#-installation)
- [Usage](#-usage)
- [How It Works](#-how-it-works)
- [Configuration](#-configuration)
- [Troubleshooting](#-troubleshooting)
- [Limitations](#-limitations)
- [Legal Notice](#-legal-notice)
- [License](#-license)

## ✨ Features

✅ **Voice-guided CAPTCHA solving** for visually impaired users  
✅ **reCAPTCHA v2/v3 support** including checkbox and image challenges  
✅ **Cloudflare Turnstile support** with automated verification  
✅ **Audio CAPTCHA assistance** with text input guidance  
✅ **Screen reader friendly** with clear verbal instructions  
✅ **Safety mechanisms** to prevent unintended actions  

## 📥 Installation

### Prerequisites
- Python 3.8+
- Windows/macOS/Linux (with GUI access)
- Microphone (for audio CAPTCHAs)
- Speakers/headphones

### Installation Steps

```bash
# Clone the repository
git clone https://github.com/yourusername/accessicaptcha.git
cd accessicaptcha

# Install dependencies
pip install -r requirements.txt

# Set up your .env file
echo "GEMINI_API_KEY=your_api_key_here" > .env