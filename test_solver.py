#!/usr/bin/env python3
"""
Test suite for Coordinate-based Web Browser Agent AI
Comprehensive tests for all components and functionality
"""

import os
import sys
import unittest
import tempfile
import time
from unittest.mock import Mock, patch, MagicMock
from PIL import Image
import numpy as np

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from solver import (
    CaptchaSolver, TextToSpeechManager, ScreenCapture, 
    GeminiAnalyzer, AutomationController, ClickCoordinate, 
    ChallengeAnalysis
)
from config import ConfigManager, apply_preset

class TestTextToSpeechManager(unittest.TestCase):
    """Test TTS functionality"""
    
    def setUp(self):
        self.tts = TextToSpeechManager()
    
    def test_initialization(self):
        """Test TTS manager initialization"""
        self.assertIsNotNone(self.tts.engine)
    
    @patch('pyttsx3.init')
    def test_speak_functionality(self, mock_init):
        """Test speech functionality"""
        mock_engine = Mock()
        mock_init.return_value = mock_engine
        
        tts = TextToSpeechManager()
        tts.speak("Test message")
        
        mock_engine.say.assert_called_with("Test message")
    
    def test_speak_with_wait(self):
        """Test blocking speech"""
        # This test would require actual TTS engine
        # In real scenario, we'd mock the engine
        pass

class TestScreenCapture(unittest.TestCase):
    """Test screen capture functionality"""
    
    def setUp(self):
        self.screen_capture = ScreenCapture()
    
    def test_initialization(self):
        """Test screen capture initialization"""
        self.assertIsNotNone(self.screen_capture.sct)
    
    def test_encode_image_base64(self):
        """Test image encoding"""
        # Create test image
        img = Image.new('RGB', (100, 100), color='red')
        
        # Test encoding
        encoded = self.screen_capture.encode_image_base64(img)
        self.assertIsInstance(encoded, str)
        self.assertTrue(len(encoded) > 0)
    
    def test_save_screenshot(self):
        """Test screenshot saving"""
        img = Image.new('RGB', (100, 100), color='blue')
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temp directory for test
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                filepath = self.screen_capture.save_screenshot(img, "test.png")
                self.assertTrue(os.path.exists("screenshots/test.png"))
            finally:
                os.chdir(original_cwd)

class TestClickCoordinate(unittest.TestCase):
    """Test ClickCoordinate data class"""
    
    def test_coordinate_creation(self):
        """Test coordinate object creation"""
        coord = ClickCoordinate(
            x=100, y=200, 
            confidence=0.95, 
            description="Test button",
            element_type="button"
        )
        
        self.assertEqual(coord.x, 100)
        self.assertEqual(coord.y, 200)
        self.assertEqual(coord.confidence, 0.95)
        self.assertEqual(coord.description, "Test button")
        self.assertEqual(coord.element_type, "button")

class TestChallengeAnalysis(unittest.TestCase):
    """Test ChallengeAnalysis data class"""
    
    def test_analysis_creation(self):
        """Test analysis object creation"""
        coords = [
            ClickCoordinate(100, 200, 0.9, "Button", "button")
        ]
        
        analysis = ChallengeAnalysis(
            challenge_type="cloudflare",
            instructions="Click the button",
            click_coordinates=coords,
            success_probability=0.85,
            next_steps=["Wait for verification"]
        )
        
        self.assertEqual(analysis.challenge_type, "cloudflare")
        self.assertEqual(len(analysis.click_coordinates), 1)
        self.assertEqual(analysis.success_probability, 0.85)

class TestGeminiAnalyzer(unittest.TestCase):
    """Test Gemini AI analyzer"""
    
    def setUp(self):
        # Use mock API key for testing
        self.analyzer = GeminiAnalyzer("test_api_key")
    
    @patch('google.generativeai.configure')
    @patch('google.generativeai.GenerativeModel')
    def test_initialization(self, mock_model, mock_configure):
        """Test Gemini analyzer initialization"""
        analyzer = GeminiAnalyzer("test_key")
        mock_configure.assert_called_with(api_key="test_key")
        mock_model.assert_called_with('gemini-2.0-flash-exp')
    
    def test_create_analysis_prompt(self):
        """Test prompt creation"""
        prompt = self.analyzer._create_analysis_prompt()
        self.assertIn("Cloudflare", prompt)
        self.assertIn("reCAPTCHA", prompt)
        self.assertIn("JSON format", prompt)
    
    def test_parse_gemini_response(self):
        """Test response parsing"""
        mock_response = '''
        {
            "challenge_type": "cloudflare",
            "instructions": "Click verification button",
            "click_coordinates": [
                {
                    "x": 500,
                    "y": 300,
                    "confidence": 0.95,
                    "description": "Verify button",
                    "element_type": "button"
                }
            ],
            "success_probability": 0.9,
            "next_steps": ["Wait for verification"]
        }
        '''
        
        analysis = self.analyzer._parse_gemini_response(mock_response)
        
        self.assertEqual(analysis.challenge_type, "cloudflare")
        self.assertEqual(len(analysis.click_coordinates), 1)
        self.assertEqual(analysis.click_coordinates[0].x, 500)

class TestAutomationController(unittest.TestCase):
    """Test automation controller"""
    
    def setUp(self):
        self.controller = AutomationController()
    
    @patch('pyautogui.moveTo')
    @patch('pyautogui.click')
    def test_click_coordinate(self, mock_click, mock_move):
        """Test coordinate clicking"""
        coord = ClickCoordinate(100, 200, 0.9, "Test", "button")
        
        result = self.controller.click_coordinate(coord)
        
        mock_move.assert_called_with(100, 200, duration=0.5)
        mock_click.assert_called_with(100, 200)
        self.assertTrue(result)
    
    def test_images_similar(self):
        """Test image similarity comparison"""
        # Create similar images
        img1 = Image.new('RGB', (100, 100), color='red')
        img2 = Image.new('RGB', (100, 100), color='red')
        
        # Test similarity
        similar = self.controller._images_similar(img1, img2)
        self.assertTrue(similar)
        
        # Create different images
        img3 = Image.new('RGB', (100, 100), color='blue')
        different = self.controller._images_similar(img1, img3)
        self.assertFalse(different)

class TestConfigManager(unittest.TestCase):
    """Test configuration management"""
    
    def setUp(self):
        self.config = ConfigManager()
    
    def test_initialization(self):
        """Test config manager initialization"""
        self.assertIsNotNone(self.config.solver)
        self.assertIsNotNone(self.config.tts)
        self.assertIsNotNone(self.config.automation)
    
    def test_validate_config(self):
        """Test configuration validation"""
        # Test valid config
        issues = self.config.validate_config()
        self.assertEqual(len(issues), 0)
        
        # Test invalid config
        self.config.solver.max_attempts = 0
        issues = self.config.validate_config()
        self.assertGreater(len(issues), 0)
    
    def test_preset_application(self):
        """Test preset configuration application"""
        original_rate = self.config.tts.rate
        
        apply_preset("accessibility_focused")
        
        # Should have changed TTS rate
        self.assertNotEqual(self.config.tts.rate, original_rate)

class TestCaptchaSolver(unittest.TestCase):
    """Test main solver functionality"""
    
    def setUp(self):
        # Mock all external dependencies
        with patch('solver.TextToSpeechManager'), \
             patch('solver.ScreenCapture'), \
             patch('solver.GeminiAnalyzer'), \
             patch('solver.AutomationController'):
            self.solver = CaptchaSolver("test_api_key")
    
    def test_initialization(self):
        """Test solver initialization"""
        self.assertIsNotNone(self.solver.tts)
        self.assertIsNotNone(self.solver.screen_capture)
        self.assertIsNotNone(self.solver.gemini)
        self.assertIsNotNone(self.solver.automation)
    
    @patch('solver.CaptchaSolver._capture_and_save_screenshot')
    @patch('solver.CaptchaSolver._execute_solution')
    def test_solve_challenge(self, mock_execute, mock_capture):
        """Test challenge solving process"""
        # Mock screenshot
        mock_img = Image.new('RGB', (100, 100))
        mock_capture.return_value = mock_img
        
        # Mock analysis
        mock_analysis = ChallengeAnalysis(
            challenge_type="cloudflare",
            instructions="Click button",
            click_coordinates=[],
            success_probability=0.9,
            next_steps=[]
        )
        self.solver.gemini.analyze_challenge.return_value = mock_analysis
        
        # Mock successful execution
        mock_execute.return_value = True
        
        result = self.solver.solve_challenge()
        self.assertTrue(result)

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_full_workflow_simulation(self):
        """Test complete workflow with mocked components"""
        # This would test the entire flow from screenshot to solution
        # In a real scenario, we'd use more sophisticated mocking
        pass
    
    def test_error_handling(self):
        """Test error handling throughout the system"""
        # Test various error conditions
        pass

def create_test_suite():
    """Create comprehensive test suite"""
    suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestTextToSpeechManager,
        TestScreenCapture,
        TestClickCoordinate,
        TestChallengeAnalysis,
        TestGeminiAnalyzer,
        TestAutomationController,
        TestConfigManager,
        TestCaptchaSolver,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    return suite

def run_specific_test(test_name):
    """Run a specific test"""
    suite = unittest.TestSuite()
    suite.addTest(unittest.TestLoader().loadTestsFromName(test_name))
    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(suite)

def main():
    """Main test runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test runner for Captcha Solver')
    parser.add_argument('--test', help='Run specific test')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--coverage', action='store_true', help='Run with coverage')
    
    args = parser.parse_args()
    
    if args.coverage:
        try:
            import coverage
            cov = coverage.Coverage()
            cov.start()
        except ImportError:
            print("Coverage module not installed. Install with: pip install coverage")
            return
    
    if args.test:
        result = run_specific_test(args.test)
    else:
        # Run all tests
        suite = create_test_suite()
        runner = unittest.TextTestRunner(verbosity=2 if args.verbose else 1)
        result = runner.run(suite)
    
    if args.coverage:
        cov.stop()
        cov.save()
        print("\nCoverage Report:")
        cov.report()
    
    # Return appropriate exit code
    sys.exit(0 if result.wasSuccessful() else 1)

if __name__ == "__main__":
    main()
